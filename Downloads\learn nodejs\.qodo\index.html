<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calclator</title>
    <style>
        .calculator-main{
            min-width: 260px;
            height: max-content;
            width: 300px;
            border: 2px solid black;
            border-radius: 5px;
            padding: 10px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .calculation-area{
            min-height: 100px;
            border: 2px solid black;
            border-radius: 5px;
            padding: 10px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            overflow-y: auto;
            overflow-x: hidden;
        }
        .display-input{
            width: 100%;
            height: 50px;
            font-size: 1.5rem;
            text-align: right;
            border: none;
            outline: none;
            background: none;
            padding: 10px;
            font-weight: bold;
        }
        .number-button{
            display : grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            padding: 10px;
        }
        [data-type="Number"]{
            background-color: #eee;
        }
        [data-type="Operator"]{
            background-color: orange;
        }
        [data-type="Equal"]{
            grid-column: span 2;
            background-color: green;
        }
        [data-type="Clear"]{
            grid-row: span 2;
            background-color: red;
        }
        [data-type="Decimal"]{
              grid-row: span 2;
            background-color: blue;
        }
        button{
            padding: 10px;
            font-size: 1.5rem;
            cursor: pointer;
            border: none;
            border-radius: 5px;
            font-weight: bold;
        }
        #result{
            min-height: 20px;
            border: 2px solid black;
            border-radius: 5px;
            padding: 5px;
            display: flex;
            flex-direction: column;
            gap: 5px;
            overflow-y: auto;
            overflow-x: hidden;
        }
    </style>
</head>
<body>
    <div class="main">
        <div class="calculator-main">
            <div id="result"></div>
            <div class="calculation-area">
                <input type="text" class="display-input" disabled>
            </div>
            <div class="number-button">
                <button data-value="7" data-type="Number">7</button>
                <button data-value="8" data-type="Number">8</button>
                <button data-value="9" data-type="Number">9</button>
                <button data-value="+" data-type="Operator">+</button>
                <button data-value="6" data-type="Number">6</button>
                <button data-value="5" data-type="Number">5</button>
                <button data-value="4" data-type="Number">4</button>
                <button data-value="-" data-type="Operator">-</button>
                <button data-value="3" data-type="Number">3</button>
                <button data-value="2" data-type="Number">2</button>
                <button data-value="1" data-type="Number">1</button>
                <button data-value="*" data-type="Operator">*</button>
                <button data-value="0" data-type="Number">0</button>
                <button data-value="/" data-type="Operator">/</button>
                <button data-value="C" data-type="Clear">C</button>
                <button data-value="." data-type="Decimal">.</button>
                <button data-value="=" data-type="Equal">=</button>
            </div>
        </div>
    </div>
</body>
<script src="./main.js"></script>
</html>