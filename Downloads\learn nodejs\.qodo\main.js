
class CalculatorCore {
    currentOperator = '';
    currentNumber = '';
    previousNumber = '';
    previousOperator = '';
    compute(operator, currentNumber, previousNumber) {
        switch (operator) {
            case '+':
                return parseFloat(previousNumber) + parseFloat(currentNumber);
            case '-':
                return parseFloat(previousNumber) - parseFloat(currentNumber);
            case '*':
                return previousNumber * currentNumber;
            case '/':
                return parseFloat(previousNumber) / parseFloat(currentNumber);
            default:
                return;
        }
    }
    clear() {
        this.currentNumber = '';
        this.previousNumber = '';
        this.currentOperator = '';
    }
    appendValue(value) {
        this.currentNumber = this.currentNumber + value;
        console.log(this.currentNumber, "current", this.previousNumber, "previous", this.currentOperator, "currentOperator");
    }
}

class DisplayManager extends CalculatorCore {
    constructor() {
        super();
    }

    parseNumber(valueType, value) {
        switch (valueType) {
            case 'equal':
                if (this.currentNumber && this.previousNumber && this.currentOperator) {
                    let value = this.compute(this.currentOperator, this.currentNumber, this.previousNumber);
                    this.previousNumber = value;
                    this.currentNumber = '';
                    this.previousOperator = '';
                    this.currentOperator = '';
                }
                break;
            case 'clear':
                this.clear();
                break;
            case 'number':
                console.log(this.currentNumber, "current", this.previousNumber, "previous", this.currentOperator, "currentOperator");
                
                this.appendValue(value);

                break;
            case 'operator':
                if (this.currentNumber === '') {
                    return;
                }
                if (this.currentNumber && !this.previousNumber) {
                    console.log("we are here")
                    this.previousNumber = this.currentNumber;
                    this.currentNumber = '';
                    this.currentOperator = value;
                }
                else if (this.currentNumber && this.previousNumber && this.currentOperator) {
                    let value1 = this.compute(this.currentOperator, this.currentNumber, this.previousNumber);
                    this.previousNumber = value1;
                    this.currentNumber = '';
                    this.previousOperator = '';
                    this.currentOperator = value;
                }
                break;
            case 'decimal':
                if (this.currentNumber.includes('.')) {
                    return;
                }
                this.currentNumber = this.currentNumber + value;
                break;

        }

    }
    updateDisplay(type, value) {
        this.parseNumber(type, value);
        if(this.previousNumber){
            this.displayInput.value = this.previousNumber + (this.currentNumber? this.currentOperator + this.currentNumber : this.currentOperator);
        }
        else{
            this.displayInput.value = this.currentNumber+this.currentOperator;
        }
        this.finalValue =  parseFloat(this.displayInput.value);
        //convert to locale string
        this.finalValue = this.finalValue.toLocaleString('en-US');
        this.result.innerHTML = this.finalValue;
        console.log(this.result, "finalValue");
    }
}
class mainCalculator extends DisplayManager {
    constructor() {
        super();
        this.finalValue = '';
        this.result = document.getElementById('result');
        this.displayInput = document.querySelector('.display-input');
        this.numberButtons = document.querySelectorAll('[data-type="Number"]');
        this.operatorButtons = document.querySelectorAll('[data-type="Operator"]');
        this.equalButton = document.querySelector('[data-type="Equal"]');
        this.clearButton = document.querySelector('[data-type="Clear"]');
        this.decimalButton = document.querySelector('[data-type="Decimal"]');

        Array.from(this.numberButtons).forEach(button => {
            button.addEventListener('click', () => {
                this.updateDisplay('number', button.dataset.value);
            });
        });
        Array.from(this.operatorButtons).forEach(button => {
            button.addEventListener('click', () => {
                this.updateDisplay('operator', button.dataset.value);
            });
        });
        this.equalButton.addEventListener('click', () => {
            this.updateDisplay('equal', '');
        });
        this.clearButton.addEventListener('click', () => {
            this.updateDisplay('clear', '');
        });
        this.decimalButton.addEventListener('click', () => {

            this.updateDisplay('decimal', '.');
        });
    }
}


const calculator = new mainCalculator();
