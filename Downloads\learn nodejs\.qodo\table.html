<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Scroll Test</title>
  <style>
    body {
      margin: 0;
      font-family: sans-serif;
      height: 3000px; /* Make the page scrollable */
      background: linear-gradient(white, lightblue);
    }

    #scroll-box {
      width: 300px;
      height: 150px;
      overflow: scroll;
      border: 2px solid black;
      margin: 50px;
      background: #f9f9f9;
    }

    #content {
      height: 1000px;
      width: 1000px;
      background: repeating-linear-gradient(45deg, #ccc, #eee 20px);
    }

    #controls {
      position: fixed;
      top: 10px;
      left: 10px;
      background: white;
      border: 1px solid gray;
      padding: 10px;
      z-index: 1000;
    }

    button {
      display: block;
      margin: 5px 0;
      width: 200px;
    }

    #output {
      font-size: 12px;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>

<div id="controls">
  <button onclick="scrollToTop()">Scroll to Top</button>
  <button onclick="scrollDown()">Scroll Down 300px</button>
  <button onclick="scrollToBox()">Scroll Box to 200px</button>
  <button onclick="scrollBoxIntoView()">Box Into View</button>
  <button onclick="logScrollInfo()">Log Scroll Info</button>
  <div id="output">Scroll info will appear here</div>
</div>

<div id="scroll-box">
  <div id="content">Scrollable Box Content</div>
</div>

<script>
  const output = document.getElementById('output');
  const scrollBox = document.getElementById('scroll-box');
  const content = document.getElementById('content');

  function scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  function scrollDown() {
    window.scrollBy({ top: 300, behavior: 'smooth' });
  }

  function scrollToBox() {
    scrollBox.scrollTo({ top: 200, left: 100,right: 100, behavior: 'smooth' });
  }

  function scrollBoxIntoView() {
    scrollBox.scrollIntoView({ behavior: 'smooth' });
  }

  function logScrollInfo() {
    output.textContent = `
🪟 Window:
window.scrollX: ${window.scrollX}
window.scrollY: ${window.scrollY}
window.innerWidth: ${window.innerWidth}
window.innerHeight: ${window.innerHeight}

📄 Document:
documentElement.scrollWidth: ${document.documentElement.scrollWidth}
documentElement.scrollHeight: ${document.documentElement.scrollHeight}
documentElement.clientWidth: ${document.documentElement.clientWidth}
documentElement.clientHeight: ${document.documentElement.clientHeight}

📦 Scroll Box:
scrollTop: ${scrollBox.scrollTop}
scrollLeft: ${scrollBox.scrollLeft}
scrollHeight: ${scrollBox.scrollHeight}
scrollWidth: ${scrollBox.scrollWidth}
clientWidth: ${scrollBox.clientWidth}
clientHeight: ${scrollBox.clientHeight}
offsetWidth: ${scrollBox.offsetWidth}
offsetHeight: ${scrollBox.offsetHeight}
    `;
  }

  // Re-log scroll info on scroll or resize
  window.addEventListener('scroll', logScrollInfo);
  window.addEventListener('resize', logScrollInfo);
  scrollBox.addEventListener('scroll', logScrollInfo);

  window.addEventListener('load', logScrollInfo);
</script>

</body>
</html>
