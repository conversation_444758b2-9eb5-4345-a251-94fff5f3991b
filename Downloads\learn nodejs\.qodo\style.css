*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    color: #101010;
    line-height: 1.5;
}
.main{
    width: 100%;
    margin: auto;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    max-width: 1200px;
    position: relative;
}

.modal{
    position: fixed;
    top: 50%;
    display: flex;
    flex-direction: column;
    gap: 20px;
    left: 50%;
    transform: translate(-50%,-50%) scale(0);
    width: 80%;
    max-width: 790px;
    z-index: 10;
    padding: 20px;
    height: 80%;
    max-height: 790px;
    overflow-y: auto;
    overflow-x: hidden;
    border: 2px solid #101010;
    border-radius: 5px;
    padding: 5px 10px;
    background-color: #fff;
    transition: all 0.4s ease;
}
.open-button{
    position: absolute;
    top: 20px;
    left: 20px;
    cursor : pointer;
}
.modal-active{
    transform: translate(-50%, -50%) scale(1);
    transition: all 0.4s ease;
}
.modal .modal-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 2px solid #101010;
}
.modal-body{
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
    padding: 10px 0;
}

#close-button{
    font-size: large;
    font-weight: bold;
    cursor: pointer;
}

.over-lay{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform: scale(0);
    z-index: 9;
    background-color: rgba(0, 0, 0, 0.5);
    pointer-events: none;
    transition: all 0.3s ease;
}
.over-lay-active{
    transform: scale(1);
    transition: transform 0.3s ease;
    pointer-events: all;
}